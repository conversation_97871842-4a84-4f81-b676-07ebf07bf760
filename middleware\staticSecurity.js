const express = require("express");
const path = require("path");
const crypto = require("crypto");
const fs = require("fs");

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map();

// Clean up rate limit store every 5 minutes
setInterval(() => {
  const now = Date.now();
  for (const [key, data] of rateLimitStore.entries()) {
    if (now - data.resetTime > 300000) { // 5 minutes
      rateLimitStore.delete(key);
    }
  }
}, 300000);

/**
 * Security middleware for static files
 */
const secureStaticMiddleware = (req, res, next) => {
  const clientIP = req.ip || req.connection.remoteAddress;
  const userAgent = req.get("User-Agent") || "";
  const referer = req.get("Referer") || "";
  
  // Rate limiting per IP
  const rateLimitKey = clientIP;
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 30; // 30 requests per minute per IP
  
  if (!rateLimitStore.has(rateLimitKey)) {
    rateLimitStore.set(rateLimitKey, {
      count: 1,
      resetTime: now + windowMs
    });
  } else {
    const rateData = rateLimitStore.get(rateLimitKey);
    if (now > rateData.resetTime) {
      rateData.count = 1;
      rateData.resetTime = now + windowMs;
    } else {
      rateData.count++;
      if (rateData.count > maxRequests) {
        return res.status(429).json({ error: "Too many requests" });
      }
    }
  }

  // Check for signed URLs (for sensitive content)
  const { token, expires, signature } = req.query;
  if (token && expires && signature) {
    if (!verifySignedUrl(req.path, token, expires, signature)) {
      return res.status(403).json({ error: "Invalid or expired token" });
    }
  }

  // User Agent filtering - block obvious bots and crawlers
  const blockedUserAgents = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /java/i,
    /go-http-client/i,
    /postman/i
  ];

  const isBlockedUserAgent = blockedUserAgents.some(pattern => pattern.test(userAgent));
  
  // Allow LINE user agents and some legitimate browsers
  const allowedUserAgents = [
    /line/i,
    /mozilla/i,
    /chrome/i,
    /safari/i,
    /edge/i,
    /firefox/i
  ];

  const isAllowedUserAgent = allowedUserAgents.some(pattern => pattern.test(userAgent));

  if (isBlockedUserAgent && !isAllowedUserAgent) {
    console.log(`Blocked request from suspicious user agent: ${userAgent}`);
    return res.status(403).json({ error: "Access denied" });
  }

  // Referer checking for additional security
  const allowedReferers = [
    /line\.me/i,
    /line-apps\.com/i,
    /line-scdn\.net/i,
    /line-apps-beta\.com/i
  ];

  // If referer exists and it's not from allowed domains, be more strict
  if (referer && !allowedReferers.some(pattern => pattern.test(referer))) {
    // For non-LINE referers, require signed URLs for sensitive content
    if (req.path.includes('/downloads/') && (!token || !signature)) {
      console.log(`Blocked direct access to sensitive content from referer: ${referer}`);
      return res.status(403).json({ error: "Direct access not allowed" });
    }
  }

  // Log access for monitoring
  console.log(`Static file access: ${req.path} from ${clientIP} (${userAgent.substring(0, 100)})`);

  // Serve the static file
  const staticPath = path.join(__dirname, "../static");
  const filePath = path.join(staticPath, req.path);

  // Security check: prevent directory traversal
  if (!filePath.startsWith(staticPath)) {
    return res.status(403).json({ error: "Access denied" });
  }

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ error: "File not found" });
  }

  // Set security headers
  res.set({
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Cache-Control': 'private, max-age=3600', // 1 hour cache
    'Referrer-Policy': 'strict-origin-when-cross-origin'
  });

  // Serve the file
  res.sendFile(filePath);
};

/**
 * Verify signed URL
 */
function verifySignedUrl(path, token, expires, signature) {
  try {
    // Check if token has expired
    const expiresTime = parseInt(expires);
    if (Date.now() > expiresTime) {
      return false;
    }

    // Verify signature
    const secret = process.env.channelSecret || 'default-secret';
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(`${path}:${token}:${expires}`)
      .digest('hex');

    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    console.error('Error verifying signed URL:', error);
    return false;
  }
}

module.exports = {
  secureStaticMiddleware
};
