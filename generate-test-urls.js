// Generate signed URLs for testing
require("dotenv").config();
const { generateSignedUrl } = require("./utils/urlSigner");

console.log("🔗 Signed URLs for Testing (Valid for 1 hour):");
console.log("=" .repeat(60));

const files = [
  "/downloads/1749166050970.jpg",
  "/downloads/1749166144172.jpg", 
  "/downloads/1749166316715.jpg",
  "/downloads/1749207528560.mp4",
  "/downloads/1749252119177_original.mp3"
];

files.forEach(file => {
  const signedUrl = generateSignedUrl(file, 60); // 1 hour expiry
  console.log(`\n📁 ${file}`);
  console.log(`🔐 ${signedUrl}`);
});

console.log("\n" + "=" .repeat(60));
console.log("💡 Copy any signed URL above and test in browser - should work!");
console.log("💡 Copy any direct URL and test in browser - should show 403 page!");
