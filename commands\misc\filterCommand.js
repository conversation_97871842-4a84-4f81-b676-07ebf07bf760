const { filterMap } = require("../../utils/filters");

module.exports = {
  command: "filter",
  aliases: [],
  category: "misc",
  description: "Shows mp3 filters list",
  requiresPrefix: true, // Set to false to enable prefix-less activation
  includes: false, // Set to true if the command should trigger when included in a message
  handler: async (client, blobClient, event, args) => {

    const supportedFilters = Object.keys(filterMap);

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: `Filter to be added to the "!mp3" command.

Supported filters: ${supportedFilters.join(", ")}.

Usage Example:
!mp3 https://www.youtube.com/watch?v=dQw4w9WgXcQ [distortion, chipmunk]

Custom filter example:
!mp3 https://www.youtube.com/watch?v=dQw4w9WgXcQ [custom=asetrate=44100*1.5,aresample=44100]

Custom + Preset example:
!mp3 https://www.youtube.com/watch?v=dQw4w9WgXcQ [custom=asetrate=44100*1.5,aresample=44100, distortion]

More about FFMpeg filters: https://ffmpeg.org/ffmpeg-filters.html


Filters must be placed inside [ ].`,
          quoteToken: event.message.quoteToken,
        }
      ]
    });
    
  }
};