const crypto = require("crypto");

/**
 * Generate a signed URL for static content
 * @param {string} path - The file path (e.g., '/downloads/video.mp4')
 * @param {number} expiresInMinutes - Expiration time in minutes (default: 60)
 * @returns {string} - Signed URL with query parameters
 */
function generateSignedUrl(path, expiresInMinutes = 60) {
  const baseUrl = process.env.baseurl || 'http://localhost:3000';
  const secret = process.env.channelSecret || 'default-secret';
  
  // Generate random token
  const token = crypto.randomBytes(16).toString('hex');
  
  // Calculate expiration time
  const expires = Date.now() + (expiresInMinutes * 60 * 1000);
  
  // Create signature
  const signature = crypto
    .createHmac('sha256', secret)
    .update(`${path}:${token}:${expires}`)
    .digest('hex');
  
  // Normalize path to prevent double slashes
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;

  // Build the signed URL
  const signedUrl = `${baseUrl}${normalizedPath}?token=${token}&expires=${expires}&signature=${signature}`;

  return signedUrl;
}

/**
 * Generate a simple static URL (for non-sensitive content)
 * @param {string} path - The file path
 * @returns {string} - Simple static URL
 */
function generateStaticUrl(path) {
  const baseUrl = process.env.baseurl || 'http://localhost:3000';
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${normalizedPath}`;
}

/**
 * Check if a file path should use signed URLs
 * @param {string} path - The file path
 * @returns {boolean} - True if should use signed URL
 */
function shouldUseSignedUrl(path) {
  const sensitiveDirectories = [
    '/downloads/',
    '/temp/',
    '/user-content/'
  ];

  // Also check for sensitive file extensions
  const sensitiveExtensions = [
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', // Images
    '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv',   // Videos
    '.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a',  // Audio
    '.pdf', '.doc', '.docx', '.zip', '.rar', '.7z'    // Documents/Archives
  ];

  const hasDirectoryMatch = sensitiveDirectories.some(dir => path.includes(dir));
  const hasExtensionMatch = sensitiveExtensions.some(ext => path.toLowerCase().endsWith(ext));

  return hasDirectoryMatch || hasExtensionMatch;
}

/**
 * Generate appropriate URL based on content sensitivity
 * @param {string} path - The file path
 * @param {number} expiresInMinutes - Expiration time for signed URLs
 * @returns {string} - Appropriate URL (signed or simple)
 */
function generateSecureUrl(path, expiresInMinutes = 60) {
  if (shouldUseSignedUrl(path)) {
    return generateSignedUrl(path, expiresInMinutes);
  } else {
    return generateStaticUrl(path);
  }
}

module.exports = {
  generateSignedUrl,
  generateStaticUrl,
  generateSecureUrl,
  shouldUseSignedUrl
};
