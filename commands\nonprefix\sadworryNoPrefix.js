module.exports = {
  command: "sadworry",
  aliases: [],
  category: "nonprefix",
  description: "Gesek kartu ajah",
  requiresPrefix: false,
  includes: false,
  handler: async (client, blobClient, event, args) => {

    const user = await client.getGroupMemberProfile(event.source.groupId, event.source.userId)

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "image",
          originalContentUrl: `${process.env.baseurl}/image/sadworry.png`,
          previewImageUrl: `${process.env.baseurl}/image/sadworry.png`,
          sender: {
            iconUrl: user.pictureUrl,
          },
        },
      ],
    });
  },
};
