# Static File Security Implementation

This document explains the security measures implemented to protect static files served by the LINE bot.

## Overview

The bot now uses a secure static file serving system instead of directly exposing files through Express static middleware. This prevents unauthorized access to downloaded content and media files.

## Security Features

### 1. Signed URLs for Sensitive Content
- Files in `/downloads/`, `/temp/`, and `/user-content/` directories require signed URLs
- URLs include a token, expiration time, and cryptographic signature
- Signatures are generated using HMAC-SHA256 with the channel secret
- Default expiration: 2 hours for videos/audio, 1 hour for images

### 2. User Agent Filtering
- Blocks obvious bots and crawlers (curl, wget, python scripts, etc.)
- Allows legitimate browsers and LINE clients
- Logs suspicious access attempts

### 3. Referer Header Verification
- Checks if requests come from LINE domains (line.me, line-apps.com, etc.)
- Requires signed URLs for sensitive content accessed from non-LINE referers
- Provides additional protection against direct browser access

### 4. Rate Limiting
- Limits requests to 30 per minute per IP address
- Prevents abuse and automated scraping
- Uses in-memory store (consider Redis for production)

### 5. Security Headers
- `X-Content-Type-Options: nosniff` - Prevents MIME type sniffing
- `X-Frame-Options: DENY` - Prevents embedding in frames
- `X-XSS-Protection: 1; mode=block` - Enables XSS protection
- `Cache-Control: private, max-age=3600` - Controls caching
- `Referrer-Policy: strict-origin-when-cross-origin` - Controls referer information

### 6. Automatic File Cleanup
- Automatically deletes files older than 24 hours
- Runs cleanup every 6 hours
- Prevents disk space accumulation
- Logs cleanup activities

### 7. Directory Traversal Protection
- Validates file paths to prevent access outside static directory
- Blocks attempts to access system files

## Implementation Details

### Files Modified
- `index.js` - Replaced static middleware with secure middleware
- `middleware/staticSecurity.js` - Main security middleware
- `utils/urlSigner.js` - URL signing utilities
- `utils/fileCleanup.js` - Automatic file cleanup
- Updated postback and command files to use secure URLs

### URL Structure

**Signed URLs (for sensitive content):**
```
https://your-domain.com/static/downloads/video.mp4?token=abc123&expires=1234567890&signature=def456
```

**Simple URLs (for public content):**
```
https://your-domain.com/static/image/line.png
```

### Configuration

The security system uses the following environment variables:
- `channelSecret` - Used for signing URLs (required)
- `baseurl` - Base URL for generating links (required)

### Monitoring

The system logs:
- All static file access attempts
- Blocked requests with reasons
- File cleanup activities
- Security violations

## Usage in Code

### Generating Secure URLs

```javascript
const { generateSecureUrl } = require("./utils/urlSigner");

// For sensitive content (automatically uses signed URLs)
const videoUrl = generateSecureUrl("/downloads/video.mp4", 120); // 2 hours expiry

// For public content (uses simple URLs)
const imageUrl = generateSecureUrl("/image/logo.png");
```

### Manual URL Signing

```javascript
const { generateSignedUrl } = require("./utils/urlSigner");

const signedUrl = generateSignedUrl("/downloads/sensitive.mp4", 60); // 1 hour expiry
```

## Security Considerations

### Strengths
- Prevents direct browser access to sensitive content
- Time-limited access reduces exposure window
- Multiple layers of protection
- Automatic cleanup prevents data accumulation

### Limitations
- Cannot completely prevent access by LINE clients (by design)
- In-memory rate limiting doesn't persist across restarts
- Relies on User-Agent headers which can be spoofed

### Recommendations for Production
1. Use Redis for rate limiting store
2. Implement IP whitelisting for known LINE servers
3. Add request logging to external monitoring system
4. Consider using CDN with additional security features
5. Regularly monitor access patterns

## Troubleshooting

### Common Issues

**Files not accessible:**
- Check if URL is properly signed
- Verify expiration time hasn't passed
- Ensure channel secret is correctly configured

**Rate limiting errors:**
- Check if IP is making too many requests
- Consider adjusting rate limits for legitimate use cases

**User agent blocked:**
- Verify the client is using an allowed user agent
- Add specific user agents to allowed list if needed

### Debugging

Enable debug logging by checking the console output for:
- Static file access logs
- Blocked request notifications
- Cleanup activity reports

## Future Enhancements

Potential improvements:
1. Integration with cloud storage (S3, Google Cloud Storage)
2. Advanced bot detection using behavioral analysis
3. Geolocation-based access control
4. Content encryption for highly sensitive files
5. Integration with LINE's official security features (if available)
