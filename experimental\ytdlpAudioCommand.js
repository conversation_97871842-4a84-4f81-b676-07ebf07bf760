const { spawn } = require("child_process");
const fs = require("fs");
const path = require("path");
const ffmpeg = require("fluent-ffmpeg");
const { applyFilters, filterMap } = require("../../utils/filters");
const { formatDuration } = require("../../utils/utils");

const ytDlpBinary = "./bin/yt-dlp.exe";

module.exports = {
  command: "mp3",
  aliases: ["yta"],
  category: "utility",
  description: "Download audio from links with optional filters",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const input = event.message.text.split(" ");
    const videoUrl = input[1];
    const filterInput = input.slice(2).join(" ");

    const maxFileSize = 200 * 1024 * 1024; // 200MB
    const supportedFilters = filterMap ? Object.keys(filterMap) : [];

    if (!videoUrl || !videoUrl.startsWith("https://")) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Invalid or missing URL.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // **Parsing Filters**
    let filters = [];
    let customFilters = [];

    if (filterInput) {
      const matches = filterInput.match(/\[([^\]]+)\]/); // Extract text within []
      if (matches) {
        const rawFilters = matches[1].split(","); // Split by comma
        rawFilters.forEach((filter) => {
          if (filter.startsWith("custom=")) {
            customFilters.push(filter.replace("custom=", "")); // Store custom filters
          } else if (supportedFilters.includes(filter)) {
            filters.push(filter); // Store preset filters
          }
        });
      }
    }

    // Fungsi helper untuk mengambil metadata video menggunakan yt-dlp dengan --dump-json
    const getVideoInfo = (videoUrl) => {
      return new Promise((resolve, reject) => {
        const infoProcess = spawn(ytDlpBinary, [videoUrl, "--dump-json"]);
        let output = "";
        infoProcess.stdout.on("data", (data) => {
          output += data.toString();
        });
        infoProcess.stderr.on("data", (data) => {
          console.error(data.toString());
        });
        infoProcess.on("close", (code) => {
          if (code !== 0) {
            reject(new Error("yt-dlp failed to fetch video info"));
          } else {
            try {
              const info = JSON.parse(output);
              resolve(info);
            } catch (err) {
              reject(err);
            }
          }
        });
        infoProcess.on("error", (err) => {
          reject(err);
        });
      });
    };

    let metadata;
    try {
      metadata = await getVideoInfo(videoUrl);

      if (metadata.is_live) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "Cannot download live videos.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }

      if (metadata.availability === "private") {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "This video is private.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }
    } catch (error) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Failed to fetch video metadata.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    const user =
      event.source.type === "group"
        ? await client.getGroupMemberProfile(event.source.groupId, event.source.userId)
        : await client.getProfile(event.source.userId);

    const downloadsDir = "./static/downloads";
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir, { recursive: true });
    }

    const timestamp = Date.now();
    const originalFilePath = path.join(downloadsDir, `${timestamp}_original.mp3`);
    const filteredFilePath = path.join(downloadsDir, `${timestamp}_filtered.mp3`);

    const argsForDownload = [videoUrl, "-x", "--audio-format", "mp3", "-o", originalFilePath];
    const downloadProcess = spawn(ytDlpBinary, argsForDownload);

    downloadProcess.stdout.on("data", (data) => {
      console.log(data.toString());
    });

    downloadProcess.stderr.on("data", (data) => {
      console.error(data.toString());
    });

    downloadProcess.on("close", async (code) => {
      if (code !== 0) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "Failed to download audio.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }

      let finalFilePath = originalFilePath;
      let duration = metadata.duration * 1000;

      if (filters.length > 0 || customFilters.length > 0) {
        try {
          await applyFilters(originalFilePath, filteredFilePath, [...filters, ...customFilters]);
          finalFilePath = filteredFilePath;
          duration = await new Promise((resolve, reject) => {
            ffmpeg.ffprobe(filteredFilePath, (err, data) =>
              err ? reject(err) : resolve(data.format.duration * 1000)
            );
          });
        } catch (error) {
          return client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: "text",
                text: "Failed to apply filters.",
                quoteToken: event.message.quoteToken,
              },
            ],
          });
        }
      }

      if (fs.statSync(finalFilePath).size > maxFileSize) {
        fs.unlinkSync(finalFilePath);
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "File size exceeds 200MB.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }

      client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "audio",
            originalContentUrl: `${process.env.baseurl}/downloads/${path.basename(finalFilePath)}`,
            duration,
          },
          {
            type: "text",
            text: `🔉 ${metadata.title}\nFilters: ${[...filters, ...customFilters].join(", ") || "Normal"}\n\nRequested by ${user.displayName}`,
            quoteToken: event.message.quoteToken,
          },
        ],
      });

      if (filters.length > 0 || customFilters.length > 0) {
        fs.unlinkSync(originalFilePath);
      }
    });

    downloadProcess.on("error", () => {
      client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Failed to download audio.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    });
  },
};
