require("dotenv").config();
const { messagingApi } = require("@line/bot-sdk");
const express = require("express");
const { loadEvents } = require("./loaders/eventLoader");
const { loadCommands } = require("./loaders/commandLoader");
const { loadPostbacks } = require("./loaders/postbackLoader");
const path = require("path");
const { setupRichMenu } = require("./richMenu");
const ngrok = require("@ngrok/ngrok");
const { checkYouTubeStatus } = require("./plugin/javascript/ytNotification");
const { spawn } = require("child_process");
const winston = require("winston");
const { updateYtDlp } = require("./plugin/javascript/ytdlpCheckUpdates");



// Konfigurasi logger dengan warna
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.colorize(), // Tambahkan warna
    winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
    winston.format.printf(
      ({ level, message, timestamp }) => `[${timestamp}] ${level}: ${message}`
    )
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({
      filename: "app.log",
      format: winston.format.combine(
        winston.format.uncolorize(),
        winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
        winston.format.printf(
          ({ level, message, timestamp }) => `[${timestamp}] ${level}: ${message}`
        )
      ),
    }),
  ],
});


// Jalankan Python API
const pythonServer = spawn(
  path.join(__dirname, "plugin/python/.venv/Scripts/python"), // Path ke Python dalam venv
  ["plugin/python/main.py"], {
    windowsHide: true,
    
  }
);

pythonServer.stdout.on("data", (data) => {
  const message = data.toString().trim();
  if (message.includes("Running on")) {
    logger.info(`Python Output: ${message}`);
  } else {
    logger.info(`Python Output: ${message}`);
  }
});

pythonServer.stderr.on("data", (data) => {
  const message = data.toString().trim();
  if (
    message.toLowerCase().includes("warning") || 
    message.toLowerCase().includes("tip") || 
    message.toLowerCase().includes("debug")
  ) {
    logger.warn(`Python Warning: ${message}`);
  } else {
    logger.info(`${message}`);
  }
});

// Pastikan server Python dimatikan saat Node.js keluar
process.on("exit", () => {
  pythonServer.kill();
});

// LINE SDK configuration
const config = {
  channelSecret: process.env.channelSecret,
};

// Create LINE SDK client
const client = new messagingApi.MessagingApiClient({
  channelAccessToken: process.env.channelAccessToken,
});

client.commands = new Map();
client.postbacks = new Map();
client.cooldowns = new Map();

const blobClient = new messagingApi.MessagingApiBlobClient({
  channelAccessToken: process.env.channelAccessToken,
});

// Express app setup
const app = express();

// Load commands, events, and postbacks
logger.info("Loading commands...");
loadCommands(client);
logger.info("Loading postbacks...");
loadPostbacks(client);
logger.info("Loading events...");
loadEvents(client, blobClient, app, config);

// Load static files
app.use("/", express.static(path.join(__dirname, "static")));

(async () => {
  logger.info("Checking yt-dlp for updates...");
  await updateYtDlp();
})();

// Listen on port
const port = process.env.PORT || 3000;
app.listen(port, () => {
  logger.info(`Server listening on port ${port}`);
});

(async function () {
  try {
    await ngrok.disconnect(process.env.baseurl.replace(/^https?:\/\//, ""));
    const listener = await ngrok.forward({
      addr: 3000,
      authtoken: process.env.NGROK_AUTH_TOKEN,
      domain: process.env.baseurl.replace(/^https?:\/\//, ""),
    });

    logger.info(`Ingress established at: ${listener.url()}`);
  } catch (err) {
    logger.error(`Failed to establish ingress: ${err.message}`);
  }
})();

setInterval(() => checkYouTubeStatus(client), 2 * 60 * 1000);

process.on("unhandledRejection", (reason, promise) => {
  logger.error(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
});
process.on("uncaughtException", (err) => {
  logger.error(`Uncaught Exception: ${err.message}`);
});
