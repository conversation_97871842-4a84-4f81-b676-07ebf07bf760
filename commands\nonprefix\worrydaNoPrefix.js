module.exports = {
  command: "worryda",
  aliases: [],
  category: "nonprefix",
  description: "Gesek kartu ajah",
  requiresPrefix: false,
  includes: false,
  handler: async (client, blobClient, event, args) => {

    const user = await client.getGroupMemberProfile(event.source.groupId, event.source.userId)

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "image",
          originalContentUrl: `${process.env.baseurl}/image/worryda.jpg`,
          previewImageUrl: `${process.env.baseurl}/image/worryda.jpg`,
          sender: {
            iconUrl: user.pictureUrl,
          },
        },
      ],
    });
  },
};
