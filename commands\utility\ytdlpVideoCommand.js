const YTDlpWrap = require("yt-dlp-wrap").default;
const ytDlpWrap = new YTDlpWrap("./bin/yt-dlp.exe");
const fs = require("fs");
const path = require("path");
const { formatDuration } = require("../../utils/utils");

module.exports = {
  command: "mp4",
  aliases: ["ytv"],
  category: "utility",
  description: "Download video from links",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const videoUrl = event.message.text.split(" ")[1]; // Use dynamic URL from args if provided
    const maxFileSize = 200 * 1024 * 1024;
    if (!videoUrl) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Please provide a valid video URL.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    } else if (!videoUrl.startsWith("https://")) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Invalid URL. Please provide a valid video URL.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    const videoId = extractVideoId(videoUrl);
    // Fetch metadata for the video
    let metadata;
    try {
      metadata = await ytDlpWrap.getVideoInfo(videoUrl);

      if (metadata.is_live) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "You cannot download live videos.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }

      if (metadata.availability === "private") {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "This video is private and cannot be downloaded.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }
    } catch (error) {
      console.error("Failed to fetch video metadata:", error);
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Failed to fetch video metadata. Please try again later.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }
    let user;

    if (event.source.type === "group") {
      // If event is from a group, use getGroupMemberProfile
      user = await client.getGroupMemberProfile(
        event.source.groupId,
        event.source.userId
      );
    } else {
      // Otherwise, use the standard getProfile for individual chat
      user = await client.getProfile(event.source.userId);
    }

    // Send initial confirmation message to the user
    let userProfile = user.displayName;
    // client.replyMessage(event.replyToken, {
    //   type: "text",
    //   text: "Downloading your video...",
    //   quoteToken: event.message.quoteToken,
    // });

    // Ensure the ./downloads directory exists
    const downloadsDir = "./static/downloads";
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir);
    }

    // Generate a timestamped filename
    const timestamp = Date.now();
    const outputFilePath = path.join(downloadsDir, `${timestamp}.mp4`);

    ytDlpWrap
      .exec([
        videoUrl, // Use the provided video URL
        "-f",
        "best",
        "-o",
        outputFilePath, // Save the video in the downloads directory
      ])
      
      .on("ytDlpEvent", (eventType, eventData) =>
        console.log(eventType, eventData)
      )
      .on("error", (error) => console.error(error))
      .on("close", () => {
        if (fs.statSync(outputFilePath).size > maxFileSize) {
          fs.unlink(outputFilePath, (err) => {
            if (err) {
              console.error("Error deleting file:", err);
            } else {
              console.log("File deleted successfully");
            }
          });
          return client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: "text",
                text: "Video size exceeds the maximum allowed size of 200 MB.",
                quoteToken: event.message.quoteToken,
              },
            ],
          });
        }
        // Determine the preview image URL
        let previewImageUrl = "";

        if (videoId) {
          // If video is from YouTube, use YouTube thumbnail
          previewImageUrl = `https://to-jpg.vercel.app/convert?url=${metadata.thumbnail}&format=jpg`;
        } else if (metadata.thumbnail) {
          // Use metadata.thumbnail if available
          previewImageUrl = metadata.thumbnail;
        } else if (
          metadata.thumbnails &&
          metadata.thumbnails[0] &&
          metadata.thumbnails[0].url
        ) {
          // Use the first thumbnail in metadata.thumbnails array if available
          previewImageUrl = metadata.thumbnails[0].url;
        } else {
          // Fallback to a default image if no thumbnail is found
          previewImageUrl = `${process.env.baseurl}/image/line.png`;
        }

        // Send the downloaded video to the user with the thumbnail
        const messageContent = [
          {
            type: "video",
            originalContentUrl: `${process.env.baseurl}/downloads/${timestamp}.mp4`, // Ensure your Express app serves the file properly
            previewImageUrl: previewImageUrl, // Use dynamic thumbnail
          },
          {
            type: "text",
            text: `📼 ${metadata.title}\n${formatDuration(
              metadata.duration
            )}\n\nRequested by ${userProfile}`,
            quoteToken: event.message.quoteToken,
          },
        ];
        client.replyMessage({
          replyToken: event.replyToken,
          messages: messageContent,
        });
        // if(event.source.type === 'group') {
        //     client.pushMessage(event.source.groupId, messageContent);
        // } else {
        //     client.pushMessage(event.source.userId, messageContent);
        // }
        // Use pushMessage to send the video
      });
  },
};

// Function to extract YouTube video ID from a URL
function extractVideoId(url) {
  const videoIdMatch = url.match(
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})|(?:https?:\/\/)?youtu\.be\/([a-zA-Z0-9_-]{11})/
  );
  return videoIdMatch ? videoIdMatch[1] || videoIdMatch[2] : null;
}
