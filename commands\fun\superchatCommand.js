const axios = require('axios');
const fs = require('fs');
const path = require('path');

module.exports = {
  command: "superchat",
  aliases: ["sc"],
  category: "fun",
  description: "Superchat 39! format: 'money;comment'",
  handler: async (client, blobClient, event, args) => {
    try {
      // Dapatkan profil user (baik dari group atau chat pribadi)
      let user;
      if (event.source.type === "group") {
        user = await client.getGroupMemberProfile(event.source.groupId, event.source.userId);
      } else {
        user = await client.getProfile(event.source.userId);
      }

      // Pastikan argumen tidak kosong
      if (!args.length) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            { 
                type: "text",
                text: "Format: !superchat jumlah;komentar",
                quoteToken: event.message.quoteToken

            }
        ]
        });
      }

      // Memisahkan input menjadi money dan comment berdasarkan pemisah ";"
      const [moneyInput, ...commentParts] = args.join(" ").split(";");
      const comment = commentParts.join(";").trim();

      // Validasi money (harus angka) dan batas maksimum
      const money = parseInt(moneyInput, 10);
      if (isNaN(money) || money <= 0) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            { 
                type: "text", 
                text: "Jumlah uang harus berupa angka positif.",
                quoteToken: event.message.quoteToken 

          }
        ]
        });
      }

      if (money > 5000000) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "Jumlah maksimum uang adalah 5000000.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }

      // Validasi komentar
      if (!comment) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            { 
                type: "text",
                text: "Komentar tidak boleh kosong!", 
                quoteToken: event.message.quoteToken
            }
        ]
        });
      }

      // Panggil API Superchat dengan parameter yang sesuai
      const superchatUrl = `https://superchat-generator.vercel.app/generate-image?name=${encodeURI(
        user.displayName
      )}&icon=${encodeURIComponent(user.pictureUrl)}&money=${encodeURIComponent(
        money
      )}&text=${encodeURI(comment)}`;
      const superchatResponse = await axios.get(superchatUrl, { responseType: 'stream' });

      // Buat nama file dengan menggunakan timestamp
      const timestamp = Date.now();
      const fileName = `${timestamp}.jpg`;
      const filePath = path.resolve(__dirname, '../../static/downloads', fileName);

      // Simpan gambar ke folder download
      const writer = fs.createWriteStream(filePath);
      superchatResponse.data.pipe(writer);

      // Tunggu hingga proses download selesai
      await new Promise((resolve, reject) => {
        writer.on('finish', resolve);
        writer.on('error', reject);
      });

      // Dapatkan URL untuk file yang diunduh
      const fileUrl = `${process.env.baseurl}/downloads/${fileName}`;

      // Balas dengan URL gambar
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "image",
            originalContentUrl: fileUrl,
            previewImageUrl: fileUrl,
          },
        ],
      });

    } catch (error) {
      console.error("Error handling superchat:", error.message);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
            {
            type: "text", 
            text: `Error: ${error.message}`,
            quoteToken: event.message.quoteToken
            }
        ]
      });
      
    }
  }
};
