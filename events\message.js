const handlers = require("../handlers/handlers");
module.exports = async (event, client, blobClient) => {
  if (event.source.type === "user" && event.source.userId !== process.env.tama)
    return;

  // Call the centralized handlers
  const isHandled = await handlers(client, blobClient, event);
  if (isHandled) return;

  const userMessage = event.message.text;
  const prefix = "!";

  let isMentionPrefix = false;

  // Check if the bot is mentioned as a prefix
  if (
    event.message.mention?.mentionees.some(
      (mention) =>
        mention.isSelf || event.message.text.startsWith("⚓ BaquaBot")
    )
  ) {
    isMentionPrefix = true;
  }

  // Check if the message only contains the bot mention without a command
  if (isMentionPrefix && userMessage.trim() === "@BaquaBot") {
    return client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "textV2",
          text: "Hai {user}! Aktivasi command pake !command atau @bot command",
          substitution: {
                  user: {
                    type: "mention",
                    mentionee: {
                      type: "user",
                      userId: event.source.userId
                    }
                  }
                }
        },
      ],
    });
  }

  // Variable to check if a prefixed command is handled
  let isCommandHandled = false;

  // Step 1: Check for prefixed commands (either `!` or bot mention)
  if (userMessage.startsWith(prefix) || isMentionPrefix) {
    const args = isMentionPrefix
      ? userMessage.split(" ").slice(1) // Ignore bot mention at the start
      : userMessage.slice(prefix.length).trim().split(/ +/);
    const commandName = args.shift().toLowerCase();

    // In the command handling section
    const cmd =
    client.commands.get(commandName) ||
    Array.from(client.commands.values()).find( // Changed to Array.find
      (cmd) => cmd.aliases && cmd.aliases.includes(commandName)
    );

    if (cmd && cmd.requiresPrefix !== false) {
      try {
        await cmd.handler(client, blobClient, event, args);
        isCommandHandled = true; // Mark that a prefixed command was handled
      } catch (error) {
        console.error(error);
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: `Error executing command: ${error.message || error}`,
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }
    } else {
      // Handle unknown command
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `Command not found: ${commandName}`,
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }
  }

  // Step 2: Handle prefix-less commands only if no prefixed command was handled
  if (!isCommandHandled) {
    client.commands.forEach(async (cmd) => {
      if (!cmd.requiresPrefix) {
        const lowerCaseMessage = userMessage.toLowerCase();
        const shouldActivate = cmd.includes
          ? [
              cmd.command.toLowerCase(),
              ...(cmd.aliases || []).map((alias) => alias.toLowerCase()),
            ].some((keyword) => lowerCaseMessage.includes(keyword))
          : [
              cmd.command.toLowerCase(),
              ...(cmd.aliases || []).map((alias) => alias.toLowerCase()),
            ].includes(lowerCaseMessage.trim());

        if (shouldActivate) {
          try {
            await cmd.handler(client, blobClient, event, []);
          } catch (error) {
            console.error(error);
            return client.replyMessage({
              replyToken: event.replyToken,
              messages: [
                {
                  type: "text",
                  text: `Error executing command: ${error.message || error}`,
                  quoteToken: event.message.quoteToken,
                },
              ],
            });
          }
        }
      }
    });
  }
};
