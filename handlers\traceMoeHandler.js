const { QuickDB } = require("quick.db");
const db = new QuickDB();
const fs = require("fs");
const path = require("path");
const axios = require("axios");
const { formatDuration } = require("../utils/utils");

module.exports = async (client, blobClient, event) => {
  const userId = event.source.userId;

  // Cek apakah pengguna diharapkan mengunggah gambar
  const awaitingImage = await db.get(`traceMoe_${userId}`);

  if (awaitingImage) {
    if (event.message.type === "image" && event.message.contentProvider.type === "line") {
      // Ambil konten gambar
      let stream = await blobClient.getMessageContent(event.message.id);
      let chunks = [];
    
      // Saat data diterima
      stream.on("data", (chunk) => {
        chunks.push(chunk); // Kumpulkan data biner
      });

      // Saat streaming selesai
      stream.on("end", async () => {
        console.log("Stream selesai, mulai menyimpan gambar.");

        // Gabungkan data ke dalam buffer
        const buffer = Buffer.concat(chunks);
        const timestamp = Date.now();
        // Definisikan path file dan simpan gambar
        const dirPath = path.join(__dirname, "../static/downloads"); // Sesuaikan path jika diperlukan
        const filePath = path.join(dirPath, `${timestamp}.jpg`);

        // Pastikan direktori ada, jika tidak, buat direktori baru
        if (!fs.existsSync(dirPath)) {
          fs.mkdirSync(dirPath, { recursive: true });
          console.log(`Direktori ${dirPath} berhasil dibuat.`);
        }

        // Simpan gambar
        fs.writeFileSync(filePath, buffer);
        console.log(`Gambar berhasil disimpan di ${filePath}`);
        await db.delete(`traceMoe_${userId}`);
        // Kirim permintaan ke API TraceMoe
        try {
          const trace = await axios.get(
            `https://api.trace.moe/search?anilistInfo&cutBorders&url=${process.env.baseurl}/downloads/${timestamp}.jpg`
          );

          // Proses hasil dari TraceMoe
          let anilist = trace.data.result[0].anilist;
          let anime = trace.data.result[0];
          let similarcheck;

          if (anime.similarity < 0.85) {
            similarcheck =
              "Gambar ini memiliki kemiripan dibawah 85%, ini mungkin bukan yang kamu cari";
          } else {
            similarcheck =
              "Saya menemukan anime yang mirip dengan gambar yang dimaksud";
          }

          // Hapus status pengguna dari database
          
          const replyText = 
          `${similarcheck}\n\nTitle: ${anilist.title.native}\n` +
          `Romaji title: ${anilist.title.romaji}\n`+
          `English title: ${anilist.title.english || "None"}\n` +
          `Synonyms: ${anilist.synonyms.map((i) => i).join(", ") || "None"}\n\n`+
          `Anilist link: https://anilist.co/anime/${anilist.id}\n` +
          `MyAnimeList link: https://myanimelist.net/anime/${anilist.idMal}\n` +
          `Episode: ${anime.episode}\n` +
          `Timestamp: ${formatDuration(anime.from)}\n` +
          `Similarity: ${(anime.similarity * 100).toFixed(2)}%`;
          // Balas ke pengguna dengan hasil pencarian
          await client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: "text",
                text: replyText,
                quoteToken: event.message.quoteToken,
              },
              {
                type: "video",
                originalContentUrl: `${anime.video}`,
                previewImageUrl: `${anime.image}`,
              },
            ],
          });
        } catch (error) {
          console.error("Error fetching TraceMoe data:", error);
          await db.delete(`traceMoe_${userId}`);
          return client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: "text",
                text: "Error fetching TraceMoe data : " + error.status,
                quoteToken: event.message.quoteToken,
              },
            ],
          });
          
        }
      });

      // Tangani kesalahan dalam streaming
      stream.on("error", async (err) => {
        await db.delete(`traceMoe_${userId}`);
        console.error("Error downloading content:", err);
      });

      return true; // Gambar berhasil ditangani
    } else {
      // Jika pengguna mengirim selain gambar
      return false;
    }
  }

  return false; // Tidak dalam mode unggah konten
};
