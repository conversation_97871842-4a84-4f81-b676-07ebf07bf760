const ffmpeg = require("fluent-ffmpeg");

// **Daftar filter preset yang bisa digunakan**
const filterMap = {
  bassboost: "equalizer=f=50:width_type=h:width=300:g=20",
  karaoke: "stereotools=mlev=0.03",
  trebleboost: "equalizer=f=3000:width_type=h:width=200:g=5",
  pitch: "asetrate=44100*1.2,aresample=44100",
  tempo: "atempo=1.5",
  slowtempo: "atempo=0.50",
  nightcore: "asetrate=44100*1.25,aresample=44100,atempo=1.25",
  vaporwave: "asetrate=44100*0.8,aresample=44100",
  reverb: "aecho=1.0:1.0:2000:0.8",
  flanger: "flanger",
  echo: "aecho=1.0:1.0:100:0.8",
  surround: "surround",
  phaser: "aphaser=in_gain=0.4",
  reverse: "areverse",
  distortion: "acrusher=bits=2:mix=1:mode=log:level_in=2.0:level_out=2.0",
  chipmunk: "asetrate=44100*1.5,aresample=44100",
  liverquad: "apulsator=hz=1",
  chorus: "chorus=0.5:0.9:50:0.4:0.25:2",
  lowpass: "lowpass=f=1000:width=200",
  stutter: "aloop=loop=1:size=44100",
  eq: "firequalizer=gain='if(lt(f,1000),20,0)'",
  destroy: "acrusher=bits=8:mix=0.5",
  earwax: "earwax",
  phaserfx: "aphaser=in_gain=0.4:out_gain=0.74:delay=3:decay=0.4:speed=0.5",
  softdist: "acrusher=level_in=2:bits=8:mode=log",
  lofi: "lowpass=f=1000,highpass=f=150,asetrate=32000,aresample=44100,acompressor=threshold=-20dB:ratio=4,afade=in:st=0:d=2",
  dying: "equalizer=f=100:t=h:width=200:g=6, equalizer=f=3000:t=h:width=200:g=4, aecho=0.8:0.9:100:0.3, chorus=0.5:0.7:50:0.4:0.3:0.5, flanger, bass=g=12, treble=g=8, stereotools=mlev=2, atempo=1.05, extrastereo=m=2, lowpass=f=3500, highpass=f=200, vibrato=f=6.5, tremolo=f=5:d=0.8"
};

/**
 * **Menerapkan filter pada file audio menggunakan FFmpeg**
 * @param {string} inputPath - Path file input (original file)
 * @param {string} outputPath - Path file output (filtered file)
 * @param {string[]} filters - Array filter yang akan diterapkan
 * @returns {Promise<void>}
 */
const applyFilters = (inputPath, outputPath, filters) => {
  return new Promise((resolve, reject) => {
    const command = ffmpeg(inputPath);

    // **Mengonversi filter preset dan filter custom menjadi string FFmpeg**
    const ffmpegFilters = filters.map((filter) => filterMap[filter] || filter).filter(Boolean);

    if (ffmpegFilters.length > 0) {
      command.audioFilters(ffmpegFilters);
    }

    command
      .save(outputPath)
      .on("end", resolve)
      .on("error", reject);
  });
};

module.exports = { applyFilters, filterMap };
